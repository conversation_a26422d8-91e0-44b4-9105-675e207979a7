// No Authentication System - Direct Access for Aanabi Pharmacy v2.0
// All features are directly accessible without any login barriers

class NoAuthManager {
    constructor() {
        this.isInitialized = true;
        console.log('✅ No authentication system - direct access enabled');
    }

    // Initialize immediately - no authentication needed
    async init() {
        this.isInitialized = true;
        console.log('✅ System ready - no authentication required');
    }

    // No authentication needed - always return true for all permissions
    hasPermission(permission) {
        return true;
    }

    // No user roles - always return false
    isAdmin() {
        return false;
    }

    // No user roles - always return false
    isStaff() {
        return false;
    }

    // Always logged in - no authentication barriers
    isLoggedIn() {
        return true;
    }

    // No current user concept - return null
    getCurrentUser() {
        return null;
    }

    // Provide user display info for compatibility
    getUserDisplayInfo() {
        return {
            name: 'Pharmacy User',
            role: 'admin',
            avatar: 'PU',
            modeText: 'Admin Mode',
            description: 'Full access to all pharmacy management features'
        };
    }

    // No login redirection needed
    redirectToLogin(message = '') {
        // Do nothing - no authentication required
    }

    // No logout functionality needed
    logout() {
        // Do nothing - no authentication to log out from
    }

    // Always ready - no authentication to initialize
    async waitForReady() {
        return true;
    }

    // No session management needed
    getSessionInfo() {
        return {
            currentUser: 'none',
            role: 'none',
            isInitialized: true,
            hasDatabase: !!(window.dbManager && window.dbManager.isReady())
        };
    }

    // No mode switching needed
    createModeSwitch() {
        return null;
    }

    // No admin password needed
    async promptAdminPassword() {
        return { success: true, message: 'No authentication required' };
    }

    // No permission filtering needed - return all items
    filterMenuItems(menuItems) {
        return menuItems;
    }

    // No mode switching listeners needed
    setupModeSwithListeners() {
        // Do nothing
    }
}

// Simplified utility functions for compatibility
const authUtils = {
    // Show all elements - no permission restrictions
    showIfPermitted(elementId, permission) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = 'block';
        }
    },

    // Show all elements - no permission restrictions
    hideIfNotPermitted(elementId, permission) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = 'block';
        }
    },

    // Remove all permission-based classes and show all elements
    applyPermissionClasses() {
        // Remove any role-based body classes
        document.body.className = '';

        // Show all admin-only elements
        document.querySelectorAll('.admin-only').forEach(el => {
            el.style.display = 'block';
        });

        // Show all staff-only elements
        document.querySelectorAll('.staff-only').forEach(el => {
            el.style.display = 'block';
        });
    },

    // No mode switch button needed
    createModeButton(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '';
        }
    }
};

// Initialize no-auth manager
const authManager = new NoAuthManager();

// Export utilities for global use
if (typeof window !== 'undefined') {
    window.authManager = authManager;
    window.authUtils = authUtils;
}

// Auto-setup when DOM is ready - remove all authentication restrictions
document.addEventListener('DOMContentLoaded', () => {
    authUtils.applyPermissionClasses();
});
