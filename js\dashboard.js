// Optimized Dashboard Manager for Aanabi Pharmacy Management System v2.0
class OptimizedDashboard {
    constructor() {
        this.isInitialized = false;
        this.dataCache = new Map();
        this.refreshInterval = null;
        this.init();
    }

    async init() {
        try {
            console.log('🏠 Initializing optimized dashboard...');
            AanabiUtils.performance.start('dashboard-init');
            
            // Wait for dependencies
            await this.waitForDependencies();
            
            // Setup user interface
            this.setupUserInterface();
            
            // Load dashboard data
            await this.loadDashboardData();
            
            // Start auto-refresh
            this.startAutoRefresh();
            
            this.isInitialized = true;
            AanabiUtils.performance.end('dashboard-init');
            console.log('✅ Optimized dashboard ready');
            
        } catch (error) {
            AanabiUtils.error.handle(error, 'Dashboard Initialization');
        }
    }

    async waitForDependencies() {
        AanabiUtils.loading.showGlobal('Initializing systems...');

        // Wait for database only (no authentication system)
        const maxAttempts = 100;
        let attempts = 0;

        while (!window.dbManager?.isReady() && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;

            if (attempts % 20 === 0) {
                AanabiUtils.loading.showGlobal(`Loading systems... (${attempts/10}s)`);
            }
        }

        if (!window.dbManager?.isReady()) {
            throw new Error('Database system failed to initialize');
        }
    }

    setupUserInterface() {
        // Update user interface
        this.updateUserInfo();

        // Start date/time updates
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 30000);
    }

    updateUserInfo() {
        // Set static user information (no authentication system)
        const userInfo = {
            name: 'Pharmacy User',
            role: 'admin',
            avatar: 'PU',
            description: 'Full access to all pharmacy management features'
        };

        AanabiUtils.dom.setText('user-name', userInfo.name);
        AanabiUtils.dom.setText('user-role', 'Administrator');
        AanabiUtils.dom.setText('user-avatar', userInfo.avatar);
        AanabiUtils.dom.setText('current-mode-text', 'Administrator');
        AanabiUtils.dom.setText('mode-description', userInfo.description);

        // Update mode indicator
        const modeIndicator = AanabiUtils.dom.get('mode-indicator');
        if (modeIndicator) {
            modeIndicator.className = `mode-indicator ${userInfo.role}`;
            modeIndicator.textContent = 'Admin Mode';
        }

        // Update body class for styling
        document.body.className = userInfo.role;
    }

    updateDateTime() {
        const now = new Date();
        
        const dateStr = now.toLocaleDateString('en-US', { 
            weekday: 'short', 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric' 
        });
        
        const timeStr = now.toLocaleTimeString('en-US', { 
            hour12: true,
            hour: '2-digit',
            minute: '2-digit'
        });
        
        AanabiUtils.dom.setText('current-date', dateStr);
        AanabiUtils.dom.setText('current-time', timeStr);
    }

    async loadDashboardData() {
        try {
            AanabiUtils.loading.showGlobal('Loading dashboard data...');
            AanabiUtils.performance.start('dashboard-data-load');
            
            // Load all data in parallel for better performance
            const [sales, medicines, customers] = await Promise.all([
                this.getCachedData('sales', () => dbManager.getAll('sales')),
                this.getCachedData('medicines', () => dbManager.getAll('medicines')),
                this.getCachedData('customers', () => dbManager.getAll('customers'))
            ]);
            
            // Update all dashboard sections in parallel
            await Promise.all([
                this.updateProfitCards(sales),
                this.updateStockOverview(medicines),
                this.updateLoyaltyPointsOverview(customers),
                this.updateDashboardStats(medicines, sales, customers),
                this.updateRecentSales(sales),
                this.updateStockAlerts(medicines),
                this.updateExpiringItems()
            ]);
            
            AanabiUtils.performance.end('dashboard-data-load');
            AanabiUtils.loading.hideGlobal();
            
        } catch (error) {
            AanabiUtils.loading.hideGlobal();
            AanabiUtils.error.handle(error, 'Dashboard Data Loading');
        }
    }

    // Caching system for better performance
    async getCachedData(key, dataLoader, cacheTime = 30000) {
        const cached = this.dataCache.get(key);
        const now = Date.now();
        
        if (cached && (now - cached.timestamp) < cacheTime) {
            return cached.data;
        }
        
        const data = await dataLoader();
        this.dataCache.set(key, { data, timestamp: now });
        return data;
    }

    updateProfitCards(sales) {
        const today = AanabiUtils.datetime.getCurrentDate();
        const todaySales = sales.filter(sale => sale.date === today);
        
        let bonusProfit = 0;
        let regularProfit = 0;
        let totalProfit = 0;
        
        todaySales.forEach(sale => {
            bonusProfit += parseFloat(sale.bonus_profit || 0);
            regularProfit += parseFloat(sale.regular_profit || 0);
            totalProfit += parseFloat(sale.total_profit || sale.profit_amount || 0);
        });
        
        // Animate profit values
        AanabiUtils.animation.animateValue('bonus-profit-amount', 0, bonusProfit, 1000, 
            value => AanabiUtils.format.currency(value));
        AanabiUtils.animation.animateValue('regular-profit-amount', 0, regularProfit, 1000, 
            value => AanabiUtils.format.currency(value));
        AanabiUtils.animation.animateValue('total-profit-amount', 0, totalProfit, 1000, 
            value => AanabiUtils.format.currency(value));
        
        // Calculate and display percentages
        if (totalProfit > 0) {
            const bonusPercent = AanabiUtils.format.percentage(bonusProfit, totalProfit);
            const regularPercent = AanabiUtils.format.percentage(regularProfit, totalProfit);
            
            AanabiUtils.dom.setText('bonus-profit-percentage', bonusPercent);
            AanabiUtils.dom.setText('regular-profit-percentage', regularPercent);
            
            // Calculate effective margin
            const totalSales = todaySales.reduce((sum, sale) => sum + parseFloat(sale.total_amount || 0), 0);
            const effectiveMargin = AanabiUtils.format.percentage(totalProfit, totalSales);
            AanabiUtils.dom.setText('total-profit-margin', effectiveMargin);
        }
    }

    updateStockOverview(medicines) {
        let totalBonusStock = 0;
        let totalInventoryValue = 0;
        
        medicines.forEach(medicine => {
            totalBonusStock += medicine.bonus_stock || 0;
            const regularValue = (medicine.regular_stock || 0) * (medicine.purchase_price || 0);
            const bonusValue = (medicine.bonus_stock || 0) * (medicine.selling_price || 0);
            totalInventoryValue += regularValue + bonusValue;
        });
        
        AanabiUtils.animation.animateValue('total-bonus-stock', 0, totalBonusStock, 1000, 
            value => AanabiUtils.format.number(value));
        AanabiUtils.animation.animateValue('total-inventory-value', 0, totalInventoryValue, 1000, 
            value => AanabiUtils.format.currency(value));
    }

    updateLoyaltyPointsOverview(customers) {
        const totalLoyaltyPoints = customers.reduce((sum, customer) => {
            return sum + (customer.loyalty_points || 0);
        }, 0);
        
        AanabiUtils.animation.animateValue('loyalty-points-outstanding', 0, totalLoyaltyPoints, 1000, 
            value => AanabiUtils.format.number(value));
    }

    async updateDashboardStats(medicines, sales, customers) {
        const today = AanabiUtils.datetime.getCurrentDate();
        const todaySales = sales.filter(sale => sale.date === today);
        
        // Calculate low stock items
        const lowStockItems = medicines.filter(medicine => {
            const totalStock = (medicine.regular_stock || 0) + (medicine.bonus_stock || 0);
            return totalStock <= (medicine.min_stock_level || 0);
        }).length;
        
        // Calculate expiring items
        const expiringItems = await this.getExpiringItemsCount();
        
        // Animate all stats
        AanabiUtils.animation.animateValue('total-medicines', 0, medicines.length, 800);
        AanabiUtils.animation.animateValue('low-stock-items', 0, lowStockItems, 800);
        AanabiUtils.animation.animateValue('expiring-items', 0, expiringItems, 800);
        AanabiUtils.animation.animateValue('today-sales-count', 0, todaySales.length, 800);
        AanabiUtils.animation.animateValue('total-customers', 0, customers.length, 800);
        
        // Get suppliers count
        try {
            const suppliers = await dbManager.getAll('suppliers');
            AanabiUtils.animation.animateValue('total-suppliers', 0, suppliers.length, 800);
        } catch (error) {
            console.warn('Could not load suppliers:', error);
        }
    }

    updateRecentSales(sales) {
        const recentSales = sales
            .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time))
            .slice(0, 5);
        
        const columns = [
            { 
                field: 'sale_id', 
                formatter: (value) => `<strong>${value}</strong>` 
            },
            { 
                field: 'date', 
                formatter: (value, row) => `${AanabiUtils.format.date(value)}<br><small>${AanabiUtils.format.time(row.time)}</small>` 
            },
            { 
                field: 'customer_name', 
                formatter: (value, row) => `${value}<br><small>${row.customer_phone}</small>` 
            },
            { field: 'medicine_name' },
            { 
                field: 'quantity', 
                formatter: (value, row) => `${value} <small class="text-muted">(${row.bonus_qty_sold || 0}+${row.regular_qty_sold || 0})</small>` 
            },
            { 
                field: 'total_amount', 
                formatter: (value) => AanabiUtils.format.currency(value) 
            },
            { 
                field: 'total_profit', 
                formatter: (value, row) => {
                    const profit = AanabiUtils.format.currency(value || row.profit_amount || 0);
                    const bonusProfit = row.bonus_profit ? `<br><small class="text-success">B: ${AanabiUtils.format.currency(row.bonus_profit)}</small>` : '';
                    return profit + bonusProfit;
                }
            },
            { 
                field: 'payment_method', 
                formatter: (value) => `<span class="status status-paid">${value || 'Cash'}</span>` 
            }
        ];
        
        AanabiUtils.table.updateTable('recent-sales-tbody', recentSales, columns);
    }

    updateStockAlerts(medicines) {
        const lowStockItems = medicines.filter(medicine => {
            const totalStock = (medicine.regular_stock || 0) + (medicine.bonus_stock || 0);
            return totalStock <= (medicine.min_stock_level || 0);
        }).slice(0, 5);
        
        const columns = [
            { 
                field: 'medicine_name', 
                formatter: (value) => `<strong>${value}</strong>` 
            },
            { 
                field: 'current_stock', 
                formatter: (value, row) => {
                    const total = (row.regular_stock || 0) + (row.bonus_stock || 0);
                    return `${total} <small class="text-muted">(${row.regular_stock || 0}+${row.bonus_stock || 0})</small>`;
                }
            },
            { 
                field: 'min_stock_level', 
                formatter: (value) => value || 0 
            },
            { 
                field: 'medicine_id', 
                formatter: (value) => `<a href="pages/purchase.html?medicine=${value}" class="btn btn-sm btn-warning">🛒 Reorder</a>` 
            }
        ];
        
        if (lowStockItems.length === 0) {
            AanabiUtils.dom.setHTML('low-stock-tbody', `
                <tr>
                    <td colspan="4" class="text-center text-success">
                        ✅ All medicines are well stocked
                    </td>
                </tr>
            `);
        } else {
            AanabiUtils.table.updateTable('low-stock-tbody', lowStockItems, columns);
        }
    }

    async updateExpiringItems() {
        try {
            const inventory = await dbManager.getAll('inventory');
            const today = new Date();
            const alertThreshold = 30; // days
            
            const expiringItems = inventory.filter(item => {
                if (!item.expiry_date) return false;
                return AanabiUtils.datetime.isExpiringSoon(item.expiry_date, alertThreshold);
            }).slice(0, 5);
            
            const columns = [
                { 
                    field: 'medicine_name', 
                    formatter: (value) => `<strong>${value}</strong>` 
                },
                { field: 'batch_number' },
                { 
                    field: 'expiry_date', 
                    formatter: (value) => AanabiUtils.format.date(value) 
                },
                { 
                    field: 'expiry_date', 
                    formatter: (value) => {
                        const daysLeft = AanabiUtils.datetime.getDaysDifference(new Date(), new Date(value));
                        const statusClass = daysLeft <= 7 ? 'status-overdue' : 'status-pending';
                        return `<span class="status ${statusClass}">${daysLeft} days</span>`;
                    }
                }
            ];
            
            if (expiringItems.length === 0) {
                AanabiUtils.dom.setHTML('expiring-items-tbody', `
                    <tr>
                        <td colspan="4" class="text-center text-success">
                            ✅ No items expiring soon
                        </td>
                    </tr>
                `);
            } else {
                AanabiUtils.table.updateTable('expiring-items-tbody', expiringItems, columns);
            }
            
        } catch (error) {
            console.warn('Could not load expiring items:', error);
        }
    }

    async getExpiringItemsCount() {
        try {
            const inventory = await dbManager.getAll('inventory');
            const alertThreshold = 30;
            
            return inventory.filter(item => {
                if (!item.expiry_date) return false;
                return AanabiUtils.datetime.isExpiringSoon(item.expiry_date, alertThreshold);
            }).length;
        } catch (error) {
            return 0;
        }
    }

    // Auto-refresh functionality
    startAutoRefresh() {
        // Refresh every 5 minutes
        this.refreshInterval = setInterval(() => {
            if (document.visibilityState === 'visible') {
                this.refreshData();
            }
        }, 5 * 60 * 1000);
        
        // Refresh when page becomes visible
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && this.isInitialized) {
                this.refreshData();
            }
        });
    }

    async refreshData() {
        try {
            console.log('🔄 Refreshing dashboard data...');
            
            // Clear cache
            this.dataCache.clear();
            
            // Reload data
            await this.loadDashboardData();
            
            AanabiUtils.alerts.showInfo('Dashboard data refreshed', 'alerts-container');
            
        } catch (error) {
            AanabiUtils.error.handle(error, 'Dashboard Refresh');
        }
    }

    // Manual refresh method
    async manualRefresh() {
        AanabiUtils.loading.showGlobal('Refreshing dashboard...');
        await this.refreshData();
        AanabiUtils.loading.hideGlobal();
    }

    // Cleanup method
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        this.dataCache.clear();
        this.isInitialized = false;
    }

    // Get dashboard statistics for debugging
    getStats() {
        return {
            isInitialized: this.isInitialized,
            cacheSize: this.dataCache.size,
            refreshActive: !!this.refreshInterval
        };
    }
}

// Initialize dashboard
let dashboardManager = null;

// Enhanced initialization function
async function initializeDashboard() {
    try {
        console.log('🏠 Starting enhanced dashboard initialization...');
        
        // Create dashboard manager
        if (!dashboardManager) {
            dashboardManager = new OptimizedDashboard();
        }
        
        // Wait for initialization
        await dashboardManager.init();
        
        console.log('✅ Enhanced dashboard initialization complete');
        
    } catch (error) {
        console.error('❌ Dashboard initialization failed:', error);
        AanabiUtils.error.handle(error, 'Dashboard Initialization');
    }
}

// Export for global use
if (typeof window !== 'undefined') {
    window.dashboardManager = dashboardManager;
    window.initializeDashboard = initializeDashboard;
}

// Auto-initialize if DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDashboard);
} else {
    initializeDashboard();
}
